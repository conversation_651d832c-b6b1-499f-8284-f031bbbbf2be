"use client";

import { Button } from '@/components/ui/button';
import { CollectionSelect } from '@/components/ui/collection-select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { firebaseFunctions, useRootContext } from '@/root-context';
import { httpsCallable } from 'firebase/functions';
import { AlertTriangle, ShoppingCart } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

interface CreateOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderType: 'seller' | 'buyer';
  onOrderCreated?: () => void;
}

export function CreateOrderDrawer({
  open,
  onOpenChange,
  orderType,
  onOrderCreated
}: CreateOrderDrawerProps) {
  const { currentUser, collections, appConfig } = useRootContext();
  const [selectedCollection, setSelectedCollection] = useState('');
  const [itemPrice, setItemPrice] = useState('');
  const [loading, setLoading] = useState(false);

  const selectedCollectionData = collections.find(c => c.id === selectedCollection);
  const price = parseFloat(itemPrice);
  const isValidPrice = !isNaN(price) && price > 0 && selectedCollectionData && price >= selectedCollectionData.floorPrice;

  const lockPercentage = orderType === 'seller'
    ? appConfig?.sellerLockPercentage || 0.2
    : appConfig?.buyerLockPercentage || 1.0;

  const lockAmount = isValidPrice ? price * lockPercentage : 0;
  const availableBalance = currentUser?.balance ? currentUser.balance.sum - currentUser.balance.locked : 0;
  const hasSufficientBalance = lockAmount <= availableBalance;

  const handleCreateOrder = async () => {
    if (!isValidPrice || !selectedCollectionData || !currentUser) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!hasSufficientBalance) {
      toast.error('Insufficient available balance');
      return;
    }

    try {
      setLoading(true);
      
      const functionName = orderType === 'seller' ? 'createOrderAsSeller' : 'createOrderAsBuyer';
      const createOrderFunction = httpsCallable(firebaseFunctions, functionName);
      
      const result = await createOrderFunction({
        collectionId: selectedCollection,
        amount: price,
        productId: `${selectedCollection}_${Date.now()}`, // Generate a unique product ID
      });

      const message = (result.data && typeof result.data === 'object' && 'message' in result.data)
        ? (result.data as { message: string }).message
        : 'Order created successfully!';
      toast.success(message);
      
      // Reset form
      setSelectedCollection('');
      setItemPrice('');
      onOpenChange(false);
      
      // Notify parent component
      if (onOrderCreated) {
        onOrderCreated();
      }
      
    } catch (error: unknown) {
      console.error('Order creation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create order. Please try again.';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedCollection('');
    setItemPrice('');
    onOpenChange(false);
  };

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content className="bg-[#232e3c] flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 z-[101] border-t border-[#3a4a5c] outline-none focus:outline-none">
          <div className="p-4 bg-[#232e3c] rounded-t-[10px] flex-1 max-h-[85vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-8 cursor-grab active:cursor-grabbing touch-manipulation" />
            
            <div className="max-w-md mx-auto pb-8 px-2 w-full">
              <Drawer.Title className="font-medium mb-4 text-lg flex items-center gap-2 text-white">
                <ShoppingCart className="w-5 h-5 text-ton-main" />
                Create {orderType === 'seller' ? 'Sell' : 'Buy'} Order
              </Drawer.Title>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton-main mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading configuration...</p>
                </div>
              ) : (
                <>
                  {/* Info Section */}
                  <div className="bg-slate-700 border border-slate-600 rounded-lg p-4 mb-6 w-full overflow-hidden">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-ton-main flex-shrink-0 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-white mb-1">Order Information</p>
                        <ul className="text-gray-300 space-y-1">
                          <li>• You need to lock <strong>{(lockPercentage * 100).toFixed(0)}%</strong> of the order amount</li>
                          <li>• Available balance: <strong>{availableBalance.toFixed(2)} TON</strong></li>
                          <li>• Price must be at least the collection floor price</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4 w-full overflow-hidden">
                    {/* Collection Selection */}
                    <div className="w-full">
                      <Label htmlFor="collection" className="text-sm font-medium text-white">
                        Collection
                      </Label>
                      <CollectionSelect
                        animated
                        collections={collections}
                        value={selectedCollection}
                        onValueChange={setSelectedCollection}
                        placeholder="Select a collection"
                        className="mt-1 w-full"
                      />
                    </div>

                    {/* Item Price */}
                    <div className="w-full">
                      <Label htmlFor="item-price" className="text-sm font-medium text-white">
                        Item Price (TON)
                      </Label>
                      <Input
                        id="item-price"
                        type="number"
                        step="0.01"
                        placeholder={selectedCollectionData ? `Min ${selectedCollectionData.floorPrice} TON` : "Enter price"}
                        value={itemPrice}
                        onChange={(e) => setItemPrice(e.target.value)}
                        className="mt-1 bg-slate-700 border-slate-600 text-white placeholder:text-gray-400 w-full"
                        min={selectedCollectionData?.floorPrice || 0}
                      />
                      {selectedCollectionData && itemPrice && price < selectedCollectionData.floorPrice && (
                        <p className="text-sm text-red-400 mt-1 break-words">
                          Price must be at least {selectedCollectionData.floorPrice} TON (floor price)
                        </p>
                      )}
                    </div>

                    {/* Lock Amount Summary */}
                    {isValidPrice && (
                      <div className="bg-slate-700 border border-slate-600 rounded-lg p-3 text-sm w-full overflow-hidden">
                        <div className="flex justify-between text-gray-300">
                          <span>Item price:</span>
                          <span className="text-white">{price.toFixed(2)} TON</span>
                        </div>
                        <div className="flex justify-between text-gray-300">
                          <span>Lock amount ({(lockPercentage * 100).toFixed(0)}%):</span>
                          <span className="text-white">{lockAmount.toFixed(2)} TON</span>
                        </div>
                        <div className="flex justify-between text-gray-300">
                          <span>Available balance:</span>
                          <span className="text-white">{availableBalance.toFixed(2)} TON</span>
                        </div>
                        <div className="flex justify-between font-medium border-t border-slate-600 pt-2 mt-2">
                          <span className="text-gray-300">Remaining after lock:</span>
                          <span className={hasSufficientBalance ? 'text-green-400' : 'text-red-400'}>
                            {(availableBalance - lockAmount).toFixed(2)} TON
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4 w-full">
                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1 border-slate-600 text-ton-black hover:bg-slate-700"
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleCreateOrder}
                        disabled={!isValidPrice || !hasSufficientBalance || loading}
                        className="flex-1 bg-ton-main hover:bg-ton-main/90"
                      >
                        {loading ? 'Creating...' : 'Create Order'}
                      </Button>
                    </div>

                    {!hasSufficientBalance && isValidPrice && (
                      <p className="text-sm text-red-400 text-center">
                        Insufficient balance to lock {lockAmount.toFixed(2)} TON
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
