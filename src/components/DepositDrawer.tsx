"use client";

import { useState, useCallback } from 'react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { toNano } from '@ton/core';
import { toast } from 'sonner';
import { AlertTriangle, Coins } from 'lucide-react';
import { useRootContext } from '@/root-context';
import { getUserById } from '@/api/auth-api';
import { CountdownPopup } from '@/components/CountdownPopup';

interface DepositDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DepositDrawer({ open, onOpenChange }: DepositDrawerProps) {
  const [tonConnectUI] = useTonConnectUI();
  const { currentUser, setCurrentUser, appConfig } = useRootContext();
  const [depositAmount, setDepositAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCountdownPopup, setShowCountdownPopup] = useState(false);

  const refetchUserAndClosePopup = useCallback(async () => {
    try {
      if (currentUser?.id) {
        const updatedUser = await getUserById(currentUser.id);
        if (updatedUser) {
          setCurrentUser(updatedUser);
          toast.success('Balance updated successfully!');
        }
      }
    } catch (error) {
      console.error('Failed to refetch user:', error);
      toast.error('Failed to update balance');
    } finally {
      setShowCountdownPopup(false);
    }
  }, [currentUser?.id, setCurrentUser]);

  const handleClosePopup = () => {
    setShowCountdownPopup(false);
  };

  const validateAmount = (amount: string): boolean => {
    if (!amount || !appConfig) return false;
    
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return false;
    
    return numAmount >= appConfig.minDepositAmount;
  };

  const isValidAmount = validateAmount(depositAmount);
  const marketplaceWallet = process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS;

  const handleDeposit = async () => {
    if (!isValidAmount || !appConfig || !marketplaceWallet) {
      toast.error('Invalid deposit amount or configuration');
      return;
    }

    if (!tonConnectUI.account?.address) {
      toast.error('Please connect your wallet first');
      return;
    }

    try {
      setLoading(true);
      
      const amount = parseFloat(depositAmount);
      const totalAmount = amount + appConfig.depositFee;
      
      // Create transaction
      const transaction = {
        validUntil: Math.floor(Date.now() / 1000) + 300, // 5 minutes
        messages: [
          {
            address: marketplaceWallet,
            amount: toNano(totalAmount.toString()).toString(),
            payload: '', // Optional: add memo/comment
          },
        ],
      };

      console.log('Sending transaction:', transaction);
      
      // Send transaction
      const result = await tonConnectUI.sendTransaction(transaction);
      
      console.log('Transaction result:', result);
      toast.success(`Deposit of ${amount} TON initiated successfully!`);

      // Reset form and close drawer
      setDepositAmount('');
      onOpenChange(false);

      // Show countdown popup
      setShowCountdownPopup(true);
      
    } catch (error) {
      console.error('Deposit failed:', error);
      toast.error('Deposit failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setDepositAmount('');
    onOpenChange(false);
  };

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-white flex flex-col rounded-t-[10px] h-fit mt-24 max-h-[80vh] fixed bottom-0 left-0 right-0 z-50">
          <div className="p-4 bg-white rounded-t-[10px] flex-1">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-gray-300 mb-8" />
            
            <div className="max-w-md mx-auto">
              <Drawer.Title className="font-medium mb-4 text-lg flex items-center gap-2">
                <Coins className="w-5 h-5 text-ton-main" />
                Deposit TON
              </Drawer.Title>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton-main mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading configuration...</p>
                </div>
              ) : (
                <>
                  {/* Disclaimer */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-yellow-800 mb-1">Important Information</p>
                        <ul className="text-yellow-700 space-y-1">
                          <li>• Minimum deposit: <strong>{appConfig?.minDepositAmount || 1} TON</strong></li>
                          <li>• Deposit fee: <strong>{appConfig?.depositFee || 0.1} TON</strong></li>
                          <li>• Total amount will be deducted from your wallet</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Deposit Amount Input */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="deposit-amount" className="text-sm font-medium">
                        Deposit Amount (TON)
                      </Label>
                      <Input
                        id="deposit-amount"
                        type="number"
                        placeholder={`Min ${appConfig?.minDepositAmount || 1} TON`}
                        value={depositAmount}
                        onChange={(e) => setDepositAmount(e.target.value)}
                        className="mt-1"
                        min={appConfig?.minDepositAmount || 1}
                        step="0.1"
                      />
                      {depositAmount && !isValidAmount && (
                        <p className="text-sm text-red-600 mt-1">
                          Amount must be at least {appConfig?.minDepositAmount || 1} TON
                        </p>
                      )}
                    </div>

                    {/* Amount Summary */}
                    {depositAmount && isValidAmount && (
                      <div className="bg-gray-50 rounded-lg p-3 text-sm">
                        <div className="flex justify-between">
                          <span>Deposit amount:</span>
                          <span>{depositAmount} TON</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Deposit fee:</span>
                          <span>{appConfig?.depositFee || 0.1} TON</span>
                        </div>
                        <div className="flex justify-between font-medium border-t pt-2 mt-2">
                          <span>Total to pay:</span>
                          <span>{(parseFloat(depositAmount) + (appConfig?.depositFee || 0.1)).toFixed(1)} TON</span>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1"
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleDeposit}
                        disabled={!isValidAmount || loading || !tonConnectUI.account?.address}
                        className="flex-1 bg-ton-main hover:bg-ton-main/90"
                      >
                        {loading ? 'Processing...' : 'Deposit'}
                      </Button>
                    </div>

                    {!tonConnectUI.account?.address && (
                      <p className="text-sm text-red-600 text-center">
                        Please connect your wallet to make a deposit
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>

      {/* Countdown Popup */}
      <CountdownPopup
        show={showCountdownPopup}
        onClose={handleClosePopup}
        onComplete={refetchUserAndClosePopup}
        initialSeconds={60}
        title="Deposit Processing"
        message="You will receive your funds within"
      />
    </Drawer.Root>
  );
}
