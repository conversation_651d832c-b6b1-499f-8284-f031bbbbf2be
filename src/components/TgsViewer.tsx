"use client";

import React, { useEffect, useState } from 'react';
import { ungzip } from 'pako';
import { Player, Controls } from '@lottiefiles/react-lottie-player';
import { Spinner } from '@telegram-apps/telegram-ui';
import { getCachedTgsData, setCachedTgsData } from '@/utils/tgs-cache';

interface TgsViewerProps {
  tgsUrl: string;
  style?: React.CSSProperties;
  className?: string;
  autoplay?: boolean;
  loop?: boolean;
  showControls?: boolean;
}

const TgsViewer: React.FC<TgsViewerProps> = ({
  tgsUrl,
  style = { height: '200px', width: '200px' },
  className = '',
  autoplay = true,
  loop = true,
  showControls = false,
}) => {
  const [lottieJson, setLottieJson] = useState<object | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!tgsUrl) {
      setLottieJson(null);
      setError(null);
      return;
    }

    // Reset error and set loading state
    setError(null);
    setLoading(true);

    // Validate URL ends with .tgs
    if (!tgsUrl.endsWith('.tgs')) {
      setError('Invalid URL: The URL does not end with .tgs');
      setLottieJson(null);
      setLoading(false);
      return;
    }

    // Check cache first
    const cachedData = getCachedTgsData(tgsUrl);
    if (cachedData) {
      setLottieJson(cachedData);
      setLoading(false);
      console.log('TGS file loaded from cache:', tgsUrl);
      return;
    }

    // Fetch the .tgs file directly (no CORS proxy needed for local files)
    fetch(tgsUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Network response was not ok (${response.status})`);
        }
        return response.arrayBuffer();
      })
      .then((buffer) => {
        if (buffer instanceof ArrayBuffer) {
          try {
            const decompressed = ungzip(new Uint8Array(buffer));
            const data = new TextDecoder('utf-8').decode(decompressed);
            const lottieJsonData: object = JSON.parse(data);

            // Cache the processed data
            setCachedTgsData(tgsUrl, lottieJsonData);

            setLottieJson(lottieJsonData);
            console.log('TGS file loaded and cached successfully:', tgsUrl);
          } catch (err) {
            console.error('TGS decompression error:', err);
            throw new Error('Error decompressing or parsing the .tgs file.');
          }
        } else {
          throw new Error('Received data is not an ArrayBuffer.');
        }
      })
      .catch((error: Error) => {
        console.error('Error fetching or parsing .tgs file:', error);
        setError(error.message);
        setLottieJson(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [tgsUrl]);

  if (loading || !lottieJson) {
    return (
      <div 
        className={`flex w-full !h-full relative items-center justify-center ${className}`}
        style={style}
      >
        <Spinner size='l'/>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-center p-4">
          <div className="text-red-500 text-sm mb-2">⚠️</div>
          <div className="text-xs text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!lottieJson) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-gray-500 text-sm">No animation data</div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Player 
        autoplay={autoplay} 
        loop={loop} 
        src={lottieJson} 
        style={style}
      >
        {showControls && (
          <Controls
            visible={true}
            buttons={['play', 'repeat', 'frame', 'debug']}
          />
        )}
      </Player>
    </div>
  );
};

export default TgsViewer;
