"use client";

import Image from "next/image";
import TgsViewer from "./TgsViewer";

interface TgsOrImageProps {
  isImage: boolean;
  imageProps?: {
    src: string;
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: "lazy" | "eager";
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    tgsUrl: string;
    style?: React.CSSProperties;
  };
}

export function TgsOrImage({ isImage, imageProps, tgsProps }: TgsOrImageProps) {
  if (isImage && imageProps) {
    return (
      <Image
        src={imageProps.src}
        alt={imageProps.alt}
        fill={imageProps.fill}
        className={imageProps.className}
        loading={imageProps.loading}
        sizes={imageProps.sizes}
        onError={imageProps.onError}
      />
    );
  }

  if (!isImage && tgsProps) {
    return (
      <TgsViewer
        tgsUrl={tgsProps.tgsUrl}
        style={tgsProps.style}
      />
    );
  }

  return null;
}
